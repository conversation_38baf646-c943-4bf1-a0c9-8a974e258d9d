import { Theme } from '@mui/material';
import Box from '@mui/material/Box';
import Divider from '@mui/material/Divider';
import Stack from '@mui/material/Stack';
import dayjs from 'dayjs';
import React from 'react';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import Typography from '@/components/Atoms/Typography';
import { Price } from '../Price/Price';
import { StyledReceiptWrapper } from './InvoiceOrderView.styled';
import { TInvoiceOrderViewProps } from './InvoiceOrderView.types';
import { calculateTax, convertToSlug, round2decimal } from '@/lib/utils/string';

dayjs.extend(utc);
dayjs.extend(timezone);

export const InvoiceOrderView: React.FC<TInvoiceOrderViewProps> = ({
  variant,
  orders,
  createdDate,
  customer,
  employee,
  paymentMethods,
  note,
  orderInvoiceId,
  branch,
  total,
  subTotal,
  totalBeforeTax,
  taxPercent,
  unPaid,
  childInvoices = [],
  newCredit,
  oldCredit,
  coupons,
  refund = 0,
  totalRounding = 0,
}) => (
  <StyledReceiptWrapper>
    <Stack gap={1.5}>
      <Box textAlign="center">
        <Typography component="h2" variant="heading-xmedium-700">
          Onsen Retreat and Spa (Singapore) Pte Ltd
        </Typography>
        <Typography component="p" variant="heading-xxsmall-700">
          GST Reg. No.: 201425725W
        </Typography>
      </Box>
      {variant === 'invoice' && (
        <Stack gap={0.5}>
          <Typography component="p" variant="body-small-400" textAlign="center">
            {branch.address}
          </Typography>
          <Typography component="p" variant="body-small-400" textAlign="center">
            {branch.phone}
          </Typography>
        </Stack>
      )}
    </Stack>
    <Stack gap={0.5}>
      <Typography component="p" variant="heading-xmedium-700" textAlign="center">
        {variant === 'order' ? 'Order' : 'Tax Invoice'} #{orderInvoiceId}
      </Typography>
      <Typography component="p" variant="body-medium-400" textAlign="center">
        {dayjs(createdDate).format('ddd, DD MMM YYYY, HH:mm')}
      </Typography>
    </Stack>
    {variant === 'invoice' && (
      <Stack flexDirection="row" justifyContent="space-between">
        <Typography component="p" variant="body-small-400">
          <Typography component="span" variant="heading-xsmall-700" textAlign="center">
            {customer?.name}
          </Typography>
        </Typography>
        <Typography component="p" variant="body-medium-400">
          Served By:{' '}
          <Typography component="span" variant="heading-xsmall-700" textAlign="center">
            {employee?.name}
          </Typography>
        </Typography>
      </Stack>
    )}
    {variant === 'order' && (
      <Stack gap={0.5}>
        <Typography component="p" variant="heading-xsmall-700">
          {customer.name}
        </Typography>
        <Stack direction="row" gap={1.5}>
          <Typography component="p" variant="body-small-400">
            Phone: {customer.phone}
          </Typography>
          {customer.rfid && (
            <Typography component="p" variant="body-small-400">
              RFID: {customer.rfid}
            </Typography>
          )}
          {customer?.rfidLocker && (
            <Typography component="p" variant="body-small-400">
              Locker: {customer.rfidLocker.padStart(3, '0')} - {customer.rfidGroup}
            </Typography>
          )}
        </Stack>
      </Stack>
    )}
    <Stack gap={1.5}>
      {childInvoices?.map(invoice => (
        <>
          <Stack gap={0.5}>
            <Typography component="p" variant="label-small-400">
              Invoice #{invoice?.code}
            </Typography>
            <Stack direction="row" justifyContent="space-between">
              <Typography component="p" variant="label-small-400" textAlign="right">
                Total
              </Typography>
              <Price
                amount={invoice?.total || 0}
                typographyProps={{
                  component: 'p',
                  variant: 'label-small-400',
                  sx: {
                    textDecoration: 'line-through',
                    textAlign: 'right',
                  },
                }}
              />
            </Stack>
            <Stack direction="row" justifyContent="space-between">
              <Typography component="p" variant="label-small-400" textAlign="right">
                Outstanding balance
              </Typography>
              <Price
                amount={invoice?.unPaid || 0}
                typographyProps={{ component: 'p', variant: 'label-small-400', textAlign: 'right' }}
              />
            </Stack>
          </Stack>
          <Divider
            sx={(theme: Theme) => ({
              opacity: 0.25,
              borderBottomWidth: '1px',
              borderColor: theme.palette.neutrals.N50.main,
            })}
          />
        </>
      ))}
      {orders.map(order => (
        <Stack key={order?.code} gap={1.5}>
          <Typography component="p" variant="body-medium-400">
            {order?.transferBy?.id ? 'Transfer ' : ''}Order#{order.code}
          </Typography>
          <Stack gap={1.5}>
            {order.items.map((item, index) => (
              <>
                <Stack gap={0.5}>
                  <Typography component="p" variant="body-medium-400" textTransform="capitalize">
                    {item.name}
                    {item?.couponCode ? ` - ${item?.couponCode}` : ''}
                  </Typography>
                  <Stack direction="row" width="100%" justifyContent="space-between" alignItems="center">
                    <Typography component="p" variant="body-medium-400">
                      {item.quantity} {item.quantity === 1 ? 'item' : 'items'}
                    </Typography>
                    <Price amount={item.price} typographyProps={{ component: 'p', variant: 'body-medium-400' }} />
                    <Price
                      amount={item.quantity * item.price}
                      typographyProps={{ component: 'p', variant: 'body-medium-400' }}
                    />
                  </Stack>
                  {item.note && (
                    <Typography component="p" variant="body-medium-400" textTransform="capitalize">
                      *Note: {item.note}
                    </Typography>
                  )}
                </Stack>
                {index !== order.items.length - 1 && (
                  <Divider
                    sx={(theme: Theme) => ({
                      opacity: 0.25,
                      borderBottomWidth: '1px',
                      borderColor: theme.palette.neutrals.N240.main,
                    })}
                  />
                )}
              </>
            ))}
          </Stack>
          <Divider
            sx={(theme: Theme) => ({
              opacity: 0.25,
              borderBottomWidth: '1px',
              borderColor: theme.palette.neutrals.N50.main,
            })}
          />
        </Stack>
      ))}
      {variant === 'invoice' && (
        <Stack direction="row" justifyContent="space-between" alignItems="center">
          <Typography component="p" variant="heading-xsmall-700">
            Total
          </Typography>
          <Price
            amount={subTotal}
            typographyProps={{ component: 'p', variant: 'heading-xsmall-700', textAlign: 'right' }}
          />
        </Stack>
      )}
      {coupons?.map(coupon => (
        <Stack key={coupon.couponCode} direction="row" justifyContent="space-between" alignItems="center">
          <Typography component="p" variant="body-medium-400">
            {coupon?.couponName || 'Discount'}
          </Typography>
          <Price
            prefix="- "
            amount={coupon?.discountMoney}
            typographyProps={{ component: 'p', variant: 'body-medium-400', textAlign: 'right' }}
          />
        </Stack>
      ))}
      {variant === 'invoice' && (
        <Stack direction="row" justifyContent="space-between" alignItems="center">
          <Typography component="p" variant="body-medium-400">
            Total Excl.Tax
          </Typography>
          <Price
            amount={subTotal - (coupons?.reduce((pre, cur) => pre + Number(cur?.discountMoney ?? 0), 0) || 0)}
            typographyProps={{ component: 'p', variant: 'body-medium-400', textAlign: 'right' }}
          />
        </Stack>
      )}
      {variant === 'invoice' && (
        <Stack direction="row" justifyContent="space-between" alignItems="center">
          <Typography component="p" variant="heading-xsmall-700">
            Tax {taxPercent}%
          </Typography>
          <Price
            amount={calculateTax(totalBeforeTax, taxPercent)}
            typographyProps={{ component: 'p', variant: 'heading-xsmall-700', textAlign: 'right' }}
          />
        </Stack>
      )}
      {totalRounding > 0 && variant === 'invoice' && (
        <Stack direction="row" justifyContent="space-between" alignItems="center">
          <Typography component="p" variant="heading-xxxsmall-700">
            Rounding Adjustment
          </Typography>
          <Stack flexDirection="row" gap={0.25}>
            <Price
              prefix="- "
              amount={totalRounding}
              typographyProps={{
                component: 'p',
                variant: 'heading-xxxsmall-700',
                textAlign: 'right',
              }}
            />
          </Stack>
        </Stack>
      )}
      <Stack direction="row" justifyContent="space-between" alignItems="center">
        <Typography component="p" variant="heading-xmedium-700">
          {variant === 'order' ? 'Total' : 'Total Incl. Tax'}
        </Typography>
        <Price
          amount={total}
          typographyProps={{
            component: 'p',
            variant: 'heading-xmedium-700',
            textAlign: 'right',
          }}
        />
      </Stack>
      {refund > 0 && (
        <Stack direction="row" justifyContent="space-between" alignItems="center">
          <Typography component="p" variant="heading-medium-700">
            Refund
          </Typography>
          <Price
            amount={refund}
            typographyProps={{
              component: 'p',
              variant: 'heading-medium-700',
              textAlign: 'right',
            }}
          />
        </Stack>
      )}
      {variant === 'invoice' && paymentMethods.length > 0 && (
        <>
          <Divider
            sx={(theme: Theme) => ({
              opacity: 0.25,
              borderBottomWidth: '1px',
              borderColor: theme.palette.neutrals.N50.main,
            })}
          />
          <Typography component="p" variant="heading-xsmall-700">
            Payment method:
          </Typography>
          {paymentMethods.map(paymentMethod => (
            <Stack
              key={convertToSlug(paymentMethod?.name)}
              direction="row"
              justifyContent="space-between"
              alignItems="center"
              paddingLeft={3}
            >
              <Typography component="p" variant="body-medium-400" textTransform="capitalize">
                {paymentMethod.name} {paymentMethod?.billCode ? `(${paymentMethod?.billCode})` : ''}
              </Typography>
              <Stack flexDirection="row" gap={0.25}>
                <Price
                  amount={paymentMethod.amount}
                  typographyProps={{
                    component: 'p',
                    variant: 'body-medium-400',
                    textAlign: 'right',
                  }}
                />
              </Stack>
            </Stack>
          ))}
          {unPaid > 0 && (
            <>
              <Divider
                sx={(theme: Theme) => ({
                  opacity: 0.25,
                  borderBottomWidth: '1px',
                  borderColor: theme.palette.neutrals.N50.main,
                })}
              />
              <Stack direction="row" justifyContent="space-between" alignItems="center">
                <Typography component="p" variant="heading-xsmall-700">
                  Balance
                </Typography>
                <Price
                  amount={unPaid}
                  typographyProps={{
                    component: 'p',
                    variant: 'heading-xsmall-700',
                    textAlign: 'right',
                  }}
                />
              </Stack>
            </>
          )}
          {(newCredit || oldCredit) && (
            <Divider
              sx={(theme: Theme) => ({
                opacity: 0.25,
                borderBottomWidth: '1px',
                borderColor: theme.palette.neutrals.N50.main,
              })}
            />
          )}
        </>
      )}
      {newCredit && (
        <Stack gap={0.5}>
          <Stack direction="row" alignItems="center" gap={1.5}>
            <Typography component="p" variant="heading-xsmall-700" minWidth="90px">
              Balance Credit:
            </Typography>
            <Typography component="p" variant="heading-xsmall-700" textAlign="right">
              {round2decimal(newCredit?.balance || 0)}
            </Typography>
          </Stack>
          <Stack direction="row" alignItems="center" gap={1.5}>
            <Typography component="p" variant="body-medium-400" minWidth="90px">
              Expired Date:
            </Typography>
            <Typography component="p" variant="body-medium-400">
              {dayjs(newCredit?.expiry).format('DD/MM/YYYY') === 'Invalid Date'
                ? newCredit?.expiry
                : dayjs(newCredit?.expiry).format('DD/MM/YYYY')}
            </Typography>
          </Stack>
        </Stack>
      )}
      {oldCredit && (
        <Stack gap={0.5}>
          <Stack direction="row" alignItems="center" gap={1.5}>
            <Typography component="p" variant="heading-xsmall-700" minWidth="90px">
              Balance Old Credit:
            </Typography>
            <Typography component="p" variant="heading-xsmall-700" textAlign="right">
              {round2decimal(oldCredit?.balance || 0)}
            </Typography>
          </Stack>
          <Stack direction="row" justifyContent="flex-start" alignItems="center" gap={1.5}>
            <Typography component="p" variant="body-medium-400" minWidth="90px">
              Expired Date:
            </Typography>
            <Typography component="p" variant="body-medium-400">
              {oldCredit?.expiry}
            </Typography>
          </Stack>
        </Stack>
      )}
      {!!note && (
        <>
          <Divider
            sx={(theme: Theme) => ({
              opacity: 0.25,
              borderBottomWidth: '1px',
              borderColor: theme.palette.neutrals.N50.main,
            })}
          />
          <Stack justifyContent="space-between" alignItems="flex-start" gap={0.5}>
            <Typography component="p" variant="body-medium-400">
              Note:
            </Typography>
            <Typography component="p" variant="body-medium-400">
              {note}
            </Typography>
          </Stack>
        </>
      )}
      {variant === 'invoice' && (
        <>
          <Stack>
            <Typography component="p" variant="body-small-400">
              All prepayments are covered by insurance and certificate of insurance will be issued immediately upon
              payment. Yunomori Onsen accords a cooling-off period of at least 5 working days (exclusive of Saturdays,
              Sundays and Public Holidays) to allow customers to seek full refund of payment made if they do not wish to
              proceed with the packages offered. This cooling-off period, however, is not applicable for services which
              have been utilised (will be based on ala carte price instead of promotion price).
            </Typography>
            <Typography component="p" variant="body-small-400">
              Confidential: All customers information is for transaction purpose only.
            </Typography>
            <Typography component="p" variant="body-small-400">
              Booking: Honouring prices quoted at the time of booking.
            </Typography>
          </Stack>

          <Stack marginTop="64px" gap={1.5} width="100%">
            <Divider
              sx={(theme: Theme) => ({
                opacity: 0.25,
                borderBottomWidth: '1px',
                borderColor: theme.palette.neutrals.N50.main,
              })}
            />
            <Typography component="p" variant="body-small-400" textAlign="center">
              Customer
            </Typography>
          </Stack>
        </>
      )}
    </Stack>
  </StyledReceiptWrapper>
);
