'use client';

import { TypographyOwnProps } from '@mui/material';
import Stack from '@mui/material/Stack';
import dayjs from 'dayjs';
import { isNumber, isUndefined } from 'lodash';
import { useEffect, useMemo, useState } from 'react';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import { TColumnTableReport } from '@/components/Atoms/ReportTable';
import Typography from '@/components/Atoms/Typography';
import Price from '@/components/Molecules/Price';
import { ReportFilter } from '@/components/Molecules/ReportFilter/ReportFilter';
import ReportHeader from '@/components/Molecules/ReportHeader';
import { TSelectOption } from '@/components/Molecules/RHFItems/RHFSelect/RHFSelect';
import ServiceReportTable from '@/components/Molecules/ServiceReportTable';
import { FORMAT_TIME_FULL } from '@/lib/constants/dateTime';
import { SWRKey } from '@/lib/constants/SWRKey';
import { useReport } from '@/lib/hooks/queries/report';
import {
  TOpeningClosingCredit,
  TOpeningClosingCreditByCustomerReport,
  TOpeningClosingCreditByInvoiceReport,
  TOpeningClosingCreditByPeriodReport,
  TOpeningClosingCreditReport,
} from '@/lib/types/entities/report';
import { EOpeningClosingCreditType, ERangeDate } from '@/lib/types/enum/report';
import { convertToOpeningClosingCreditReport } from '@/lib/utils/report';
import { concatenateNames, formatInvoiceCode, toQueryString } from '@/lib/utils/string';
import { customPalettes } from '@/theme/customPalettes';
import { StyledContainer } from '../Report.styled';

dayjs.extend(utc);
dayjs.extend(timezone);

type TActions = 'edit' | 'delete' | 'create' | 'edit';

type TTableActions = 'edit' | 'delete';

type TFormValues = {
  startDate?: string;
  endDate?: string;
  type?: TSelectOption;
};

type TOpeningClosingCreditProps = {
  canExport?: boolean;
};

export const OpeningClosingCredit: React.FC<TOpeningClosingCreditProps> = ({ canExport = false }) => {
  const [filterValues, setFilterValues] = useState<TFormValues>({
    startDate: dayjs().startOf('month').format(FORMAT_TIME_FULL),
    endDate: dayjs().endOf('month').format(FORMAT_TIME_FULL),
    type: {
      id: EOpeningClosingCreditType['PERIOD'],
      name: EOpeningClosingCreditType['PERIOD'],
    },
  });
  const [dataReport, setDataReport] = useState<TOpeningClosingCreditReport[]>([]);
  const [invoiceCustomerDataReport, setInvoiceCustomerDataReport] = useState<TOpeningClosingCreditReport[]>([]);
  const [currentPeriod, setCurrentPeriod] = useState<{
    month: string;
    page: number;
    limit: number;
    total: number;
  }>({
    month: '',
    page: 1,
    total: 0,
    limit: SWRKey.REPORT.LIMIT,
  });
  const shouldFetchPeriod = filterValues?.type?.id === EOpeningClosingCreditType['PERIOD'];
  const shouldFetchInvoiceOrCustomer =
    filterValues?.type?.id === EOpeningClosingCreditType['INVOICE'] ||
    filterValues?.type?.id === EOpeningClosingCreditType['CUSTOMER'];

  const { data: periodReport } = useReport<TOpeningClosingCreditByPeriodReport, TFormValues, TOpeningClosingCredit>(
    'report-opening-closing-credit',
    shouldFetchPeriod
      ? {
          ...filterValues,
          type: {
            id: EOpeningClosingCreditType['PERIOD'],
            name: EOpeningClosingCreditType['PERIOD'],
          },
        }
      : undefined
  );

  const { data: creditReport } = useReport<
    TOpeningClosingCreditByCustomerReport | TOpeningClosingCreditByInvoiceReport,
    TFormValues
  >(
    'report-opening-closing-credit',
    shouldFetchInvoiceOrCustomer && dayjs(currentPeriod?.month, 'MM-YYYY').isValid()
      ? {
          ...filterValues,
          startDate: dayjs(currentPeriod?.month, 'MM-YYYY')
            .startOf('month')
            .format(FORMAT_TIME_FULL),
          endDate: dayjs(currentPeriod?.month, 'MM-YYYY')
            .endOf('month')
            .format(FORMAT_TIME_FULL),
        }
      : undefined,
    shouldFetchInvoiceOrCustomer
      ? {
          page: currentPeriod.page,
          limit: currentPeriod.limit,
        }
      : undefined
  );

  useEffect(() => {
    if (filterValues?.type?.id === EOpeningClosingCreditType['PERIOD']) {
      setDataReport(
        convertToOpeningClosingCreditReport(
          periodReport?.sum
            ? [
                {
                  month: 'SUM',
                  first_date: '',
                  last_date: '',
                  credits: periodReport?.sum || [],
                },
                ...(periodReport?.data || []),
              ]
            : periodReport?.data || [],
          EOpeningClosingCreditType['PERIOD']
        )
      );
      setInvoiceCustomerDataReport([]);
    }
    if (
      filterValues?.type?.id === EOpeningClosingCreditType['CUSTOMER'] ||
      filterValues?.type?.id === EOpeningClosingCreditType['INVOICE']
    ) {
      const foundIndex = periodReport?.data?.findIndex(item => item.month === currentPeriod.month);
      if (!isNumber(foundIndex) || foundIndex <= -1 || !periodReport?.data?.[foundIndex]) return;

      setInvoiceCustomerDataReport(prev => {
        const newData = convertToOpeningClosingCreditReport(
          currentPeriod.page === 1
            ? [...prev, periodReport?.data?.[foundIndex], ...(creditReport?.data || [])]
            : creditReport?.data || [],
          filterValues?.type?.id as EOpeningClosingCreditType,
          currentPeriod.month
        );
        const uniqueNewData = newData.filter(newItem => {
          if (!prev.some(prevItem => prevItem.month === newItem.month)) {
            return true;
          }
          return !prev.some(
            prevItem =>
              prevItem.month === newItem.month &&
              prevItem.customerCode === newItem.customerCode &&
              prevItem.invoiceCode === newItem.invoiceCode
          );
        });
        return [...prev, ...uniqueNewData];
      });
    }
  }, [filterValues?.type, periodReport?.data, creditReport?.data, periodReport?.sum]);

  useEffect(() => {
    if (
      (filterValues?.type?.id === EOpeningClosingCreditType['CUSTOMER'] ||
        filterValues?.type?.id === EOpeningClosingCreditType['INVOICE']) &&
      periodReport?.data?.[0]?.month
    ) {
      setCurrentPeriod({
        month: periodReport?.data?.[0]?.month,
        page: 1,
        total: creditReport?.total || 1,
        limit: SWRKey.REPORT.LIMIT,
      });
    }
  }, [filterValues?.type, periodReport?.data, periodReport?.sum]);

  const getCellStyle = (month?: string) => {
    const color =
      month === 'SUM' ||
      (!month?.includes('_') &&
        dayjs(month, 'MM-YYYY').isValid() &&
        filterValues?.type?.id !== EOpeningClosingCreditType['PERIOD'])
        ? customPalettes?.success?.main
        : customPalettes?.neutrals?.N50?.main;

    const variant: TypographyOwnProps['variant'] =
      month === 'SUM' ||
      (!month?.includes('_') &&
        dayjs(month, 'MM-YYYY').isValid() &&
        filterValues?.type?.id !== EOpeningClosingCreditType['PERIOD'])
        ? 'heading-medium-700'
        : 'body-medium-400';

    return { color, variant };
  };

  const dataWithMonthHeader = useMemo(() => {
    if (filterValues?.type?.id !== EOpeningClosingCreditType['PERIOD']) return dataReport;
    const result: any[] = [];
    let lastMonth: any = null;
    dataReport.forEach((row, idx) => {
      if (row.month === 'SUM') {
        result.push(row);
        return;
      }
      if (row.month !== lastMonth) {
        result.push({ isMonthHeader: true, month: row.month });
        lastMonth = row.month;
      }
      result.push(row);
    });
    return result;
  }, [dataReport, filterValues?.type?.id]);

  const COLUMNS: TColumnTableReport<TOpeningClosingCreditReport & { isMonthHeader?: boolean }>[] = [
    {
      name: 'month',
      label: '',
      render: (row: TOpeningClosingCreditReport & { isMonthHeader?: boolean }, index?: number) => {
        const dayJS = dayjs(row?.month, 'MM-YYYY');
        if (row.isMonthHeader) {
          return (
            <Typography variant="heading-small-700" color={customPalettes?.success?.main} textTransform="uppercase">
              {dayjs(row.month, 'MM-YYYY').format('MMMM YYYY')}
            </Typography>
          );
        }
        if (
          filterValues?.type?.id !== EOpeningClosingCreditType['PERIOD'] &&
          (isUndefined(row?.customerCode) || !isUndefined(row?.invoiceCode)) &&
          (!isUndefined(row?.customerCode) || isUndefined(row?.invoiceCode))
        ) {
          return (
            <Typography
              textAlign="center"
              display="block"
              variant="heading-medium-700"
              color={customPalettes?.success?.main}
              textTransform="uppercase"
            >
              {dayJS.format('MMMM YYYY')}
            </Typography>
          );
        }
        if (dayJS.isValid() && isUndefined(row?.customerCode) && isUndefined(row?.invoiceCode)) {
          if (row?.value?.expired > 0) return '';
          return (
            <Typography
              variant={
                filterValues?.type?.id === EOpeningClosingCreditType['PERIOD'] ? 'body-medium-400' : 'heading-small-700'
              }
              color={
                filterValues?.type?.id === EOpeningClosingCreditType['PERIOD']
                  ? customPalettes?.neutrals?.N50?.main
                  : customPalettes?.success?.main
              }
              textTransform="uppercase"
            >
              {dayJS.format('MMMM YYYY')}
            </Typography>
          );
        }

        if (!isUndefined(row?.customerCode)) {
          return (
            <Stack flexDirection="row" justifyContent="flex-start" gap={1.5} alignItems="center">
              <Typography variant="body-medium-400" />
              <Typography variant="body-medium-400" width="80px">
                {row?.customerCode || ''}
              </Typography>
              <Typography
                variant="body-medium-400"
                // sx={{ maxWidth: '120px', textOverflow: 'ellipsis', overflow: 'hidden', whiteSpace: 'nowrap' }}
              >
                {concatenateNames(row?.firstName || '', row?.lastName || '')}
              </Typography>
            </Stack>
          );
        }
        if (!isUndefined(row?.invoiceCode)) {
          return (
            <Stack flexDirection="row" justifyContent="flex-start" gap={1.5} alignItems="center">
              <Typography variant="body-medium-400" />
              <Typography variant="body-medium-400">{formatInvoiceCode(row?.invoiceCode)}</Typography>
            </Stack>
          );
        }

        return (
          <Typography variant="heading-medium-700" color={customPalettes?.success?.main}>
            TOTAL
          </Typography>
        );
      },
    },
    {
      name: 'quantity',
      label: 'QUANTITY',
      children: [
        {
          name: 'quantity',
          label: 'OPENING',
          tableCellProps: { align: 'center' },
          render: (row: TOpeningClosingCreditReport) =>
            typeof row?.quantity?.opening === 'number' ? (
              <Price amount={row?.quantity?.opening} showSymbol={false} typographyProps={getCellStyle(row?.month)} />
            ) : (
              <></>
            ),
        },
        {
          name: 'quantity',
          label: 'PURCHASE',
          tableCellProps: { align: 'center' },
          render: (row: TOpeningClosingCreditReport) => (
            <Price amount={row?.quantity?.purchase} showSymbol={false} typographyProps={getCellStyle(row?.month)} />
          ),
        },
        {
          name: 'quantity',
          label: 'USE',
          tableCellProps: { align: 'center' },
          render: (row: TOpeningClosingCreditReport) => (
            <Price amount={row?.quantity?.use} showSymbol={false} typographyProps={getCellStyle(row?.month)} />
          ),
        },
        {
          name: 'quantity',
          label: 'EXPIRED',
          tableCellProps: { align: 'center' },
          render: (row: TOpeningClosingCreditReport) => {
            const style = getCellStyle(row?.month);
            return (
              <Price amount={row?.quantity?.expired} showSymbol={false} typographyProps={getCellStyle(row?.month)} />
            );
          },
        },
        {
          name: 'quantity',
          label: 'CLOSING',
          tableCellProps: { align: 'center' },
          render: (row: TOpeningClosingCreditReport) =>
            typeof row?.quantity?.closing === 'number' ? (
              <Price amount={row?.quantity?.closing} showSymbol={false} typographyProps={getCellStyle(row?.month)} />
            ) : (
              <></>
            ),
        },
        {
          name: 'quantity',
          label: 'TYPE',
          tableCellProps: { align: 'center' },
          render: (row: TOpeningClosingCreditReport) => {
            const style = getCellStyle(row?.month);
            return (
              <Typography color={style.color} variant={style.variant}>
                {row?.creditType}
              </Typography>
            );
          },
        },
      ],
      tableCellProps: { align: 'center' },
    },
    {
      name: 'value',
      label: 'VALUE',
      children: [
        {
          name: 'value',
          label: 'OPENING',
          tableCellProps: { align: 'center' },

          render: (row: TOpeningClosingCreditReport) =>
            typeof row?.value?.opening === 'number' ? (
              <Price amount={row?.value?.opening} showSymbol={false} typographyProps={getCellStyle(row?.month)} />
            ) : (
              <></>
            ),
        },
        {
          name: 'value',
          label: 'PURCHASE',
          tableCellProps: { align: 'center' },
          render: (row: TOpeningClosingCreditReport) => (
            <Price amount={row?.value?.purchase} showSymbol={false} typographyProps={getCellStyle(row?.month)} />
          ),
        },
        {
          name: 'value',
          label: 'USE',
          tableCellProps: { align: 'center' },
          render: (row: TOpeningClosingCreditReport) => (
            <Price amount={row?.value?.use} showSymbol={false} typographyProps={getCellStyle(row?.month)} />
          ),
        },
        {
          name: 'value',
          label: 'EXPIRED',
          tableCellProps: { align: 'center' },
          render: (row: TOpeningClosingCreditReport) => (
            <Price amount={row?.value?.expired} showSymbol={false} typographyProps={getCellStyle(row?.month)} />
          ),
        },
        {
          name: 'value',
          label: 'CLOSING',
          tableCellProps: { align: 'center' },
          render: (row: TOpeningClosingCreditReport) =>
            typeof row?.value?.closing === 'number' ? (
              <Price amount={row?.value?.closing} showSymbol={false} typographyProps={getCellStyle(row?.month)} />
            ) : (
              <></>
            ),
        },
        {
          name: 'expiryDate',
          label: 'EXPIRY DATE',
          tableCellProps: { align: 'center' },
          render: (row: TOpeningClosingCreditReport) => {
            const style = getCellStyle(row?.month);
            return (
              <Typography variant={style.variant} color={style.color}>
                {row?.expiryDate && dayjs.utc(row?.expiryDate).isValid()
                  ? dayjs(row?.expiryDate).format('DD/MM/YYYY')
                  : ''}
              </Typography>
            );
          },
        },
      ],
      tableCellProps: { align: 'center' },
    },
  ];

  const groupByColumns = useMemo(() => {
    if (filterValues?.type?.id === EOpeningClosingCreditType['CUSTOMER']) {
      return ['customerCode', 'month'];
    }
    if (filterValues?.type?.id === EOpeningClosingCreditType['INVOICE']) {
      return ['invoiceCode', 'month'];
    }
    if (filterValues?.type?.id === EOpeningClosingCreditType['PERIOD']) {
      return ['month'];
    }
  }, [filterValues?.type?.id]);

  return (
    <StyledContainer>
      <ReportFilter<TFormValues>
        onFilter={(value: TFormValues) => {
          let newValue = value;
          // If rangeDateType is TODAY, override startDate/endDate to today
          if ((value as any).rangeDateType === 'TODAY') {
            newValue = {
              ...value,
              startDate: dayjs().startOf('day').format(FORMAT_TIME_FULL),
              endDate: dayjs().endOf('day').format(FORMAT_TIME_FULL),
            };
          }
          setFilterValues(newValue);
          setDataReport([]);
          setInvoiceCustomerDataReport([]);
          setCurrentPeriod(pre => ({
            ...pre,
            month: '',
            page: 1,
            total: 0,
          }));
        }}
        fieldOptionNames={['type', 'rangeDate']}
        useFormProps={{
          defaultValues: {
            startDate: dayjs().startOf('month').format(FORMAT_TIME_FULL),
            endDate: dayjs().endOf('month').format(FORMAT_TIME_FULL),
            rangeDateType: ERangeDate['SELECT_DATE'],
            type: {
              id: EOpeningClosingCreditType['PERIOD'],
            },
          } as unknown as TFormValues,
        }}
      />
      <ReportHeader
        title="OPENING CLOSING CREDIT"
        url={`${SWRKey.REPORT.exportByName(
          'report-opening-closing-credit'
        )}&fileName=opening-closing-credit-report.csv&${toQueryString({
          ...filterValues,
        })}`}
        canExport={canExport}
      />
      <ServiceReportTable<TOpeningClosingCreditReport & { isMonthHeader?: boolean }>
        rows={
          filterValues?.type?.id === EOpeningClosingCreditType['PERIOD']
            ? dataWithMonthHeader
            : invoiceCustomerDataReport
        }
        columns={COLUMNS}
        showSTT={false}
        groupByColumns={groupByColumns}
        page={currentPeriod.page}
        onLoadMore={() => {
          const foundIndex =
            typeof periodReport?.data?.findIndex(item => item.month === currentPeriod.month) !== 'number'
              ? -1
              : periodReport?.data?.findIndex(item => item.month === currentPeriod.month);
          if (foundIndex > -1 && periodReport?.data?.[foundIndex + 1]?.month) {
            setCurrentPeriod(pre => ({
              ...pre,
              month: periodReport?.data?.[foundIndex + 1]?.month,
              page: 1,
              total: 0,
            }));
            return;
          }
          setCurrentPeriod(pre => {
            const newPage = pre.page + 1;
            if (newPage > currentPeriod.total) return pre;
            return {
              ...pre,
              page: newPage,
            };
          });
        }}
      />
    </StyledContainer>
  );
};
